#!/usr/bin/env python3
"""
TEST CORRECTION MESSAGES JARVIS - JEAN-LUC PASSAVE
Test de la correction du format des messages pour Gradio
"""

import gradio as gr
import time

def validate_message_format(history):
    """Valide et corrige le format des messages pour Gradio type='messages'"""
    if not history:
        return []
    
    validated = []
    for item in history:
        if isinstance(item, dict):
            # Format déjà correct
            if "role" in item and "content" in item:
                validated.append({
                    "role": str(item["role"]),
                    "content": str(item["content"])
                })
        elif isinstance(item, (list, tuple)) and len(item) >= 2:
            # Convertir format tuple/liste vers dict
            validated.extend([
                {"role": "user", "content": str(item[0])},
                {"role": "assistant", "content": str(item[1])}
            ])
        else:
            # Format invalide, ignorer
            continue
    
    return validated

def test_chat_function(message: str, history: list):
    """Fonction de test pour le chat avec validation des messages"""
    
    # Valider et corriger le format de l'historique
    history = validate_message_format(history)
    
    if not message.strip():
        return history, ""
    
    # Simuler une réponse
    response = f"✅ Message reçu: '{message}' - Format validé avec succès !"
    
    # Ajouter au format correct
    history.append({"role": "user", "content": message})
    history.append({"role": "assistant", "content": response})
    
    return history, ""

# Interface de test
with gr.Blocks(title="Test Correction Messages") as demo:
    gr.Markdown("# 🔧 Test Correction Format Messages")
    gr.Markdown("Interface de test pour vérifier la correction du format des messages")
    
    with gr.Row():
        with gr.Column():
            chatbot = gr.Chatbot(
                label="Chat Test",
                height=400,
                type='messages',  # Format messages
                show_copy_button=True
            )
            
            with gr.Row():
                message_input = gr.Textbox(
                    placeholder="Tapez votre message...",
                    label="Message",
                    scale=4
                )
                send_btn = gr.Button("Envoyer", variant="primary", scale=1)
            
            clear_btn = gr.Button("Effacer", variant="secondary")
    
    # Événements
    send_btn.click(
        fn=test_chat_function,
        inputs=[message_input, chatbot],
        outputs=[chatbot, message_input]
    )
    
    message_input.submit(
        fn=test_chat_function,
        inputs=[message_input, chatbot],
        outputs=[chatbot, message_input]
    )
    
    clear_btn.click(
        fn=lambda: ([], ""),
        outputs=[chatbot, message_input]
    )

if __name__ == "__main__":
    print("🚀 Lancement du test de correction des messages...")
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        show_error=True
    )
