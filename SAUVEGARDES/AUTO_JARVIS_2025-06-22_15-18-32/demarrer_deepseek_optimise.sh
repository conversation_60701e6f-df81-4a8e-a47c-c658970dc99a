#!/bin/bash

echo "🚀 Démarrage DeepSeek R1 8B AGENT 1 - Configuration ULTRA-OPTIMISÉE"

# AGENT 1 - PORT 8000
llama-server \
    --model "/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf" \
    --host 0.0.0.0 \
    --port 8000 \
    --ctx-size 4096 \
    --threads 4 \
    --n-gpu-layers 16 \
    --batch-size 512 \
    --ubatch-size 128 \
    --n-predict 1024 \
    --timeout 120 \
    --flash-attn \
    --mlock \
    --verbose
