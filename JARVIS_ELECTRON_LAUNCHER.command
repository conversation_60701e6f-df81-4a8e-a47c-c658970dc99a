#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS VALIDÉ - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application validée

echo "🤖 JARVIS ELECTRON LAUNCHER VALIDÉ"
echo "=================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des anciens processus..."
pkill -f "jarvis_architecture_multi_fenetres.py" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
sleep 2

# Vérifier les fichiers
if [ -f "jarvis_electron_nouveau.js" ]; then
    echo "✅ Application Electron validée trouvée"
else
    echo "❌ Application Electron manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier Electron
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron..."
    npm install
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS BACKEND..."
echo "🧠 Mémoire Thermique + Multi-Agents"

# Démarrer JARVIS en arrière-plan
source jarvis_env/bin/activate && python3 jarvis_architecture_multi_fenetres.py &
JARVIS_PID=$!
echo "✅ JARVIS Backend démarré (PID: $JARVIS_PID)"

# Attendre que JARVIS soit prêt
echo "⏳ Attente du démarrage complet de JARVIS..."
sleep 8

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON VALIDÉE..."
echo "🎯 Interface complète Apple Silicon M4"
echo "🧠 Dashboard + Multi-Fenêtres"
echo ""

# Démarrer l'application Electron validée
npm start

echo ""
echo "🔄 Application fermée"
echo "🛑 Arrêt de JARVIS..."
kill $JARVIS_PID 2>/dev/null || true
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
