#!/usr/bin/env python3
"""
🔬 ALLEN BRAIN ATLAS EXTRACTOR - DONNÉES RÉELLES
===============================================
Extraction complète des données neuronales réelles depuis l'API officielle
Allen Brain Atlas avec toutes les structures et connexions.

Jean<PERSON><PERSON>ave - JARVIS Electron
"""

import requests
import json
import time
import pandas as pd
from datetime import datetime

class AllenBrainExtractor:
    """Extracteur complet Allen Brain Atlas"""
    
    def __init__(self):
        self.base_url = "http://api.brain-map.org/api/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'JARVIS-Allen-Extractor/1.0',
            'Accept': 'application/json'
        })
        
        print("🔬 Allen Brain Atlas Extractor initialisé")
        print(f"🌐 API Base : {self.base_url}")
    
    def get_all_brain_structures(self):
        """Récupère TOUTES les structures cérébrales"""
        
        print("🧠 Extraction complète structures cérébrales...")
        
        try:
            url = f"{self.base_url}/data/query.json"
            params = {
                "criteria": "model::Structure",
                "include": "structure_sets",
                "num_rows": 2000  # Maximum pour récupérer tout
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            structures = []
            
            if "msg" in data:
                for struct in data["msg"]:
                    structure_data = {
                        "id": struct.get("id"),
                        "name": struct.get("name", "Unknown"),
                        "acronym": struct.get("acronym", ""),
                        "parent_id": struct.get("parent_structure_id"),
                        "depth": struct.get("depth", 0),
                        "graph_order": struct.get("graph_order", 0),
                        "color_hex": struct.get("color_hex_triplet", ""),
                        "hemisphere_id": struct.get("hemisphere_id"),
                        "weight": struct.get("weight", 0)
                    }
                    structures.append(structure_data)
            
            # Tri par profondeur et ordre
            structures.sort(key=lambda x: (x["depth"], x["graph_order"]))
            
            print(f"✅ {len(structures)} structures extraites")
            return structures
            
        except Exception as e:
            print(f"❌ Erreur extraction structures: {e}")
            return []
    
    def get_connectivity_matrix(self):
        """Récupère la matrice de connectivité réelle"""
        
        print("🔗 Extraction matrice de connectivité...")
        
        try:
            # API pour les expériences de connectivité
            url = f"{self.base_url}/data/query.json"
            params = {
                "criteria": "model::Experiment",
                "include": "structure,specimen",
                "num_rows": 500
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            experiments = []
            
            if "msg" in data:
                for exp in data["msg"]:
                    exp_data = {
                        "id": exp.get("id"),
                        "name": exp.get("name", ""),
                        "structure_id": exp.get("structure_id"),
                        "specimen_id": exp.get("specimen_id"),
                        "workflow_state": exp.get("workflow_state"),
                        "published": exp.get("published", False),
                        "storage_directory": exp.get("storage_directory", "")
                    }
                    experiments.append(exp_data)
            
            print(f"✅ {len(experiments)} expériences de connectivité extraites")
            return experiments
            
        except Exception as e:
            print(f"❌ Erreur connectivité: {e}")
            return []
    
    def get_gene_expression_data(self):
        """Récupère les données d'expression génique"""
        
        print("🧬 Extraction données expression génique...")
        
        try:
            url = f"{self.base_url}/data/query.json"
            params = {
                "criteria": "model::Gene",
                "num_rows": 200
            }
            
            response = self.session.get(url, params=params, timeout=20)
            response.raise_for_status()
            
            data = response.json()
            genes = []
            
            if "msg" in data:
                for gene in data["msg"]:
                    gene_data = {
                        "id": gene.get("id"),
                        "acronym": gene.get("acronym", ""),
                        "name": gene.get("name", ""),
                        "entrez_id": gene.get("entrez_id"),
                        "chromosome": gene.get("chromosome", ""),
                        "homologene_id": gene.get("homologene_id")
                    }
                    genes.append(gene_data)
            
            print(f"✅ {len(genes)} gènes extraits")
            return genes
            
        except Exception as e:
            print(f"❌ Erreur expression génique: {e}")
            return []
    
    def get_reference_spaces(self):
        """Récupère les espaces de référence 3D"""
        
        print("📐 Extraction espaces de référence 3D...")
        
        try:
            url = f"{self.base_url}/data/query.json"
            params = {
                "criteria": "model::ReferenceSpace",
                "num_rows": 50
            }
            
            response = self.session.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            spaces = []
            
            if "msg" in data:
                for space in data["msg"]:
                    space_data = {
                        "id": space.get("id"),
                        "name": space.get("name", ""),
                        "resolution": space.get("resolution", 0),
                        "section_thickness": space.get("section_thickness", 0),
                        "spacing_x": space.get("spacing_x", 0),
                        "spacing_y": space.get("spacing_y", 0),
                        "spacing_z": space.get("spacing_z", 0)
                    }
                    spaces.append(space_data)
            
            print(f"✅ {len(spaces)} espaces de référence extraits")
            return spaces
            
        except Exception as e:
            print(f"❌ Erreur espaces référence: {e}")
            return []
    
    def extract_complete_dataset(self):
        """Extraction complète de toutes les données Allen Brain Atlas"""
        
        print("🚀 EXTRACTION COMPLÈTE ALLEN BRAIN ATLAS")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. Structures cérébrales
        structures = self.get_all_brain_structures()
        time.sleep(1)  # Respect API limits
        
        # 2. Connectivité
        connectivity = self.get_connectivity_matrix()
        time.sleep(1)
        
        # 3. Expression génique
        genes = self.get_gene_expression_data()
        time.sleep(1)
        
        # 4. Espaces de référence
        reference_spaces = self.get_reference_spaces()
        
        # 📊 COMPILATION DATASET COMPLET
        complete_dataset = {
            "extraction_info": {
                "timestamp": datetime.now().isoformat(),
                "duration_seconds": round(time.time() - start_time, 2),
                "api_source": "Allen Brain Atlas Official API",
                "extractor": "JARVIS Electron - Jean-Luc Passave"
            },
            "brain_structures": {
                "count": len(structures),
                "data": structures
            },
            "connectivity_experiments": {
                "count": len(connectivity),
                "data": connectivity
            },
            "gene_expression": {
                "count": len(genes),
                "data": genes
            },
            "reference_spaces": {
                "count": len(reference_spaces),
                "data": reference_spaces
            },
            "statistics": {
                "total_structures": len(structures),
                "total_experiments": len(connectivity),
                "total_genes": len(genes),
                "total_reference_spaces": len(reference_spaces),
                "data_quality": "REAL_ALLEN_BRAIN_DATA"
            }
        }
        
        return complete_dataset
    
    def save_dataset(self, dataset, filename="allen_brain_complete.json"):
        """Sauvegarde le dataset complet"""
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Dataset sauvegardé : {filename}")
            print(f"📊 Taille : {len(json.dumps(dataset))} caractères")
            
            # Sauvegarde CSV pour analyse
            if dataset["brain_structures"]["data"]:
                df_structures = pd.DataFrame(dataset["brain_structures"]["data"])
                df_structures.to_csv("allen_brain_structures.csv", index=False)
                print("📊 Structures CSV : allen_brain_structures.csv")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
    
    def generate_summary_report(self, dataset):
        """Génère un rapport de synthèse"""
        
        stats = dataset["statistics"]
        
        report = f"""
🔬 RAPPORT EXTRACTION ALLEN BRAIN ATLAS
======================================
📅 Extraction : {dataset['extraction_info']['timestamp']}
⏱️ Durée : {dataset['extraction_info']['duration_seconds']}s
🌐 Source : {dataset['extraction_info']['api_source']}

📊 DONNÉES EXTRAITES :
• Structures cérébrales : {stats['total_structures']}
• Expériences connectivité : {stats['total_experiments']}
• Gènes analysés : {stats['total_genes']}
• Espaces de référence : {stats['total_reference_spaces']}

✅ Qualité : {stats['data_quality']}

🧠 EXEMPLES STRUCTURES :"""
        
        # Ajouter quelques exemples
        if dataset["brain_structures"]["data"]:
            for i, struct in enumerate(dataset["brain_structures"]["data"][:5]):
                report += f"\n• {struct['name']} ({struct['acronym']}) - ID: {struct['id']}"
        
        return report

def main():
    """Fonction principale d'extraction"""
    
    print("🔬 ALLEN BRAIN ATLAS EXTRACTOR")
    print("Jean-Luc Passave - JARVIS Electron")
    print("=" * 50)
    
    # Initialisation
    extractor = AllenBrainExtractor()
    
    # Extraction complète
    dataset = extractor.extract_complete_dataset()
    
    # Sauvegarde
    extractor.save_dataset(dataset)
    
    # Rapport
    report = extractor.generate_summary_report(dataset)
    print(report)
    
    return dataset

if __name__ == "__main__":
    main()
