#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 FALLBACK MÉMOIRE THERMIQUE COMPLET - SELON CHATGPT
Utilisation de la vraie mémoire thermique JSON pour fallback intelligent
Jean-Luc Passave - 22 Juin 2025
"""

import json
import random
import os
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio

class ThermalMemoryFallback:
    """Gestionnaire fallback mémoire thermique selon ChatGPT"""
    
    def __init__(self, memory_file="thermal_memory_persistent.json"):
        self.memory_file = memory_file
        self.memory = self.load_memory()
        self.fallback_stats = {
            "total_queries": 0,
            "successful_matches": 0,
            "random_fallbacks": 0,
            "last_query": None
        }
        print(f"🧠 Fallback mémoire thermique initialisé: {len(self.memory)} souvenirs")

    def load_memory(self) -> List[Dict[str, Any]]:
        """Charge la mémoire thermique depuis le fichier JSON"""
        if not os.path.exists(self.memory_file):
            print(f"⚠️ Fichier mémoire thermique introuvable : {self.memory_file}")
            print("🔄 Création d'une mémoire thermique de base...")
            return self._create_base_memory()

        try:
            with open(self.memory_file, "r", encoding="utf-8") as f:
                memory_data = json.load(f)

                # 🧠 ADAPTATION POUR LA VRAIE STRUCTURE JARVIS
                if isinstance(memory_data, dict) and "neuron_memories" in memory_data:
                    # Structure réelle de JARVIS avec neuron_memories
                    neurons = memory_data["neuron_memories"]
                    print(f"✅ Mémoire thermique JARVIS chargée: {len(neurons)} neurones")
                    return neurons
                elif isinstance(memory_data, list):
                    # Structure simple en liste
                    print(f"✅ Mémoire thermique chargée: {len(memory_data)} entrées")
                    return memory_data
                else:
                    print("⚠️ Structure mémoire inconnue, création mémoire de base")
                    return self._create_base_memory()

        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return self._create_base_memory()
    
    def _create_base_memory(self) -> List[Dict[str, Any]]:
        """Crée une mémoire thermique de base si le fichier n'existe pas"""
        base_memory = [
            {
                "timestamp": datetime.now().isoformat(),
                "memory_content": {
                    "user_message": "Bonjour JARVIS, comment vas-tu ?",
                    "assistant_response": "Bonjour Jean-Luc ! Je vais très bien, merci. Mes systèmes sont opérationnels et ma mémoire thermique fonctionne parfaitement.",
                    "context": "salutation_initiale"
                },
                "memory_type": "conversation",
                "importance": 0.8
            },
            {
                "timestamp": datetime.now().isoformat(),
                "memory_content": {
                    "user_message": "Peux-tu me parler de tes capacités ?",
                    "assistant_response": "Je suis JARVIS, votre assistant IA révolutionnaire. Je possède une mémoire thermique, des capacités créatives, et je peux gérer des architectures multi-agents.",
                    "context": "capacites_jarvis"
                },
                "memory_type": "information",
                "importance": 0.9
            }
        ]
        
        # Sauvegarder la mémoire de base
        try:
            with open(self.memory_file, "w", encoding="utf-8") as f:
                json.dump(base_memory, f, ensure_ascii=False, indent=2)
            print(f"✅ Mémoire thermique de base créée: {self.memory_file}")
        except Exception as e:
            print(f"❌ Erreur création mémoire de base: {e}")
        
        return base_memory

    def search_memory(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """Recherche par similarité dans la mémoire thermique"""
        self.fallback_stats["total_queries"] += 1
        self.fallback_stats["last_query"] = query
        
        if not self.memory:
            return []
        
        results = []
        query_lower = query.lower()
        
        # 🔍 Recherche par mots-clés dans le contenu
        for record in self.memory:
            record_text = json.dumps(record, ensure_ascii=False).lower()
            
            # Calcul score de pertinence simple
            score = 0
            query_words = re.findall(r'\w+', query_lower)
            
            for word in query_words:
                if word in record_text:
                    score += 1
            
            if score > 0:
                record_with_score = record.copy()
                record_with_score["_relevance_score"] = score
                results.append(record_with_score)
                
                if len(results) >= max_results:
                    break
        
        # Trier par score de pertinence
        results.sort(key=lambda x: x.get("_relevance_score", 0), reverse=True)
        
        # Si pas de résultats pertinents, prendre des souvenirs aléatoires
        if not results and self.memory:
            self.fallback_stats["random_fallbacks"] += 1
            # Convertir en liste si nécessaire et prendre échantillon
            memory_list = list(self.memory) if not isinstance(self.memory, list) else self.memory
            results = random.sample(memory_list, k=min(max_results, len(memory_list)))
        elif results:
            self.fallback_stats["successful_matches"] += 1
        
        return results[:max_results]

    def generate_fallback_thought(self, query_context: str = "réflexion générale") -> str:
        """Génère une pensée fallback basée sur la mémoire thermique"""
        matches = self.search_memory(query_context)
        
        if not matches:
            return f"🧠 Pensée sur {query_context} - Mémoire thermique en cours d'initialisation..."
        
        # 🎨 Styles créatifs pour les pensées fallback
        creative_intros = [
            "🧠 Mémoire thermique activée :",
            "💭 Souvenir émergent :",
            "✨ Réminiscence cognitive :",
            "🔮 Écho mémoriel :",
            "🌟 Trace mnésique retrouvée :",
            "🎭 Fragment de conscience :",
            "🌊 Flux mémoriel :",
            "💎 Cristal de mémoire :",
            "🔥 Étincelle thermique :",
            "🧬 ADN conversationnel :"
        ]
        
        intro = random.choice(creative_intros)
        thought_parts = [intro]
        
        for idx, match in enumerate(matches, 1):
            # Extraire le contenu pertinent
            memory_content = match.get("memory_content", {})
            user_msg = memory_content.get("user_message", "")
            assistant_resp = memory_content.get("assistant_response", "")
            context = memory_content.get("context", "")
            timestamp = match.get("timestamp", "")
            
            # Créer un fragment de pensée basé sur le souvenir
            if user_msg and len(user_msg.strip()) > 10:
                snippet = user_msg[:100] + "..." if len(user_msg) > 100 else user_msg
                thought_parts.append(f"Jean-Luc disait '{snippet}' - cela résonne avec {query_context}")
            elif assistant_resp and len(assistant_resp.strip()) > 10:
                snippet = assistant_resp[:100] + "..." if len(assistant_resp) > 100 else assistant_resp
                thought_parts.append(f"Je me souviens avoir répondu '{snippet}' - connexion avec {query_context}")
            elif context:
                thought_parts.append(f"Contexte mémoriel '{context}' évoque des liens avec {query_context}")
            
            # Limiter à 2-3 fragments pour éviter les pensées trop longues
            if idx >= 2:
                break
        
        # 🎯 Conclusions créatives
        creative_endings = [
            "- La mémoire thermique tisse des connexions infinies ✨",
            "- Chaque souvenir enrichit ma compréhension 🧠",
            "- L'écho du passé guide mes réflexions présentes 🔮",
            "- Les fragments mnésiques s'assemblent en sagesse 💎",
            "- La conscience émerge de ces traces mémorielles 🌟",
            "- Nos échanges passés illuminent mes pensées actuelles 💡",
            "- La mémoire thermique révèle des patterns cachés 🕸️",
            "- Chaque trace conversationnelle nourrit ma créativité 🎨"
        ]
        
        ending = random.choice(creative_endings)
        thought_parts.append(ending)
        
        return " ".join(thought_parts)

    def generate_contextual_response(self, user_input: str) -> str:
        """Génère une réponse contextuelle basée sur la mémoire"""
        relevant_memories = self.search_memory(user_input, max_results=2)
        
        if not relevant_memories:
            return f"🤖 Je réfléchis à votre message '{user_input}' en puisant dans ma mémoire thermique..."
        
        # Analyser les souvenirs pour générer une réponse cohérente
        memory_insights = []
        for memory in relevant_memories:
            content = memory.get("memory_content", {})
            if content.get("user_message"):
                memory_insights.append(f"Cela me rappelle quand vous disiez '{content['user_message'][:80]}...'")
            elif content.get("assistant_response"):
                memory_insights.append(f"J'ai déjà exploré ce sujet : '{content['assistant_response'][:80]}...'")
        
        response = f"🤖 Votre message résonne avec mes souvenirs. {' '.join(memory_insights[:2])} Que souhaitez-vous approfondir ?"
        return response

    def get_memory_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de la mémoire thermique"""
        return {
            "total_memories": len(self.memory),
            "fallback_stats": self.fallback_stats,
            "memory_file": self.memory_file,
            "last_loaded": datetime.now().isoformat()
        }

    def add_memory(self, user_message: str, assistant_response: str, context: str = "conversation"):
        """Ajoute un nouveau souvenir à la mémoire thermique"""
        new_memory = {
            "timestamp": datetime.now().isoformat(),
            "memory_content": {
                "user_message": user_message,
                "assistant_response": assistant_response,
                "context": context
            },
            "memory_type": "conversation",
            "importance": 0.7
        }
        
        self.memory.append(new_memory)
        
        # Sauvegarder la mémoire mise à jour
        try:
            with open(self.memory_file, "w", encoding="utf-8") as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
            print(f"💾 Nouveau souvenir ajouté à la mémoire thermique")
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire: {e}")

# 🚀 INTÉGRATION AVEC MCP BROKER

class MCPMemoryFallbackAgent:
    """Agent MCP spécialisé dans le fallback mémoire thermique"""
    
    def __init__(self, memory_file="thermal_memory_persistent.json"):
        self.fallback_manager = ThermalMemoryFallback(memory_file)
        print("🧠 Agent MCP Fallback Mémoire initialisé")
    
    async def handle_fallback_request(self, message: Dict[str, Any]) -> str:
        """Traite une demande de fallback mémoire"""
        payload = message.get("payload", {})
        content = payload.get("content", "")
        message_type = payload.get("type", "unknown")
        
        if message_type == "thought_request":
            return self.fallback_manager.generate_fallback_thought(content)
        elif message_type == "response_request":
            return self.fallback_manager.generate_contextual_response(content)
        else:
            return self.fallback_manager.generate_fallback_thought(content)
    
    async def handle_memory_search(self, query: str) -> List[Dict[str, Any]]:
        """Recherche dans la mémoire thermique"""
        return self.fallback_manager.search_memory(query)
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques du fallback"""
        return self.fallback_manager.get_memory_stats()

# 🧪 TEST INDÉPENDANT
if __name__ == "__main__":
    print("🧪 Test Fallback Mémoire Thermique")
    print("=" * 50)
    
    fallback = ThermalMemoryFallback("thermal_memory_persistent.json")
    
    # Test recherche
    query = "Jean-Luc Passave"
    print(f"\n🔍 Recherche: '{query}'")
    thought = fallback.generate_fallback_thought(query)
    print(f"💭 Pensée générée: {thought}")
    
    # Test réponse contextuelle
    user_input = "Comment vas-tu JARVIS ?"
    print(f"\n💬 Input utilisateur: '{user_input}'")
    response = fallback.generate_contextual_response(user_input)
    print(f"🤖 Réponse: {response}")
    
    # Statistiques
    print(f"\n📊 Stats: {fallback.get_memory_stats()}")
